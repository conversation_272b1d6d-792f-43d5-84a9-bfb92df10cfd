defmodule GenSchemasIntegrationTest do
  use ExUnit.Case, async: false

  @moduletag :integration

  # Clean up generated modules after each test and set up database
  setup do
    # Clean up before each test to ensure clean state
    cleanup_generated_modules()
    cleanup_generated_files()

    # Set up database connection for the test
    :ok = Ecto.Adapters.SQL.Sandbox.checkout(SampleApp.Repo)
    Ecto.Adapters.SQL.Sandbox.mode(SampleApp.Repo, {:shared, self()})

    on_exit(fn ->
      cleanup_generated_modules()
      cleanup_generated_files()
    end)
  end

  describe "mix drops.relation.gen_schemas" do
    test "generates valid Ecto schema modules for all tables" do
      # Run the mix task directly using the Igniter API
      igniter = Igniter.new()

      # Set up the task arguments
      args = %Igniter.Mix.Task.Args{
        argv: ["--app", "SampleApp", "--namespace", "SampleApp.Generated", "--sync", "false"],
        argv_flags: [
          "--app",
          "SampleApp",
          "--namespace",
          "SampleApp.Generated",
          "--sync",
          "false"
        ]
      }

      igniter = %{igniter | args: args}

      # Make it non-interactive
      igniter =
        igniter
        |> Igniter.assign(:prompt_on_git_changes?, false)
        |> Igniter.assign(:quiet_on_no_changes?, true)

      # Run the task
      result_igniter = Mix.Tasks.Drops.Relation.GenSchemas.igniter(igniter)

      # Apply the changes
      Igniter.do_or_dry_run(result_igniter, yes: true)

      # Compile and load the generated modules
      compile_generated_modules()

      # Verify the generated modules can be loaded and are valid Ecto schemas
      assert_valid_schema_module(SampleApp.Generated.Users, "users")
      assert_valid_schema_module(SampleApp.Generated.Posts, "posts")
    end

    test "generates schemas for specific tables only" do
      # Run the mix task directly for only the users table
      igniter = Igniter.new()

      # Set up the task arguments
      args = %Igniter.Mix.Task.Args{
        argv: [
          "--app",
          "SampleApp",
          "--namespace",
          "SampleApp.Generated",
          "--tables",
          "users",
          "--sync",
          "false"
        ],
        argv_flags: [
          "--app",
          "SampleApp",
          "--namespace",
          "SampleApp.Generated",
          "--tables",
          "users",
          "--sync",
          "false"
        ]
      }

      igniter = %{igniter | args: args}

      # Make it non-interactive
      igniter =
        igniter
        |> Igniter.assign(:prompt_on_git_changes?, false)
        |> Igniter.assign(:quiet_on_no_changes?, true)

      # Run the task
      result_igniter = Mix.Tasks.Drops.Relation.GenSchemas.igniter(igniter)

      # Apply the changes
      Igniter.do_or_dry_run(result_igniter, yes: true)

      # Compile and load the generated modules
      compile_generated_modules()

      # Verify only the users module was generated
      assert_valid_schema_module(SampleApp.Generated.Users, "users")
      refute Code.ensure_loaded?(SampleApp.Generated.Posts)
    end

    test "sync mode updates existing schemas" do
      # First, generate the schemas
      igniter = Igniter.new()

      args = %Igniter.Mix.Task.Args{
        argv: ["--app", "SampleApp", "--namespace", "SampleApp.Generated", "--sync", "false"],
        argv_flags: [
          "--app",
          "SampleApp",
          "--namespace",
          "SampleApp.Generated",
          "--sync",
          "false"
        ]
      }

      igniter = %{igniter | args: args}

      igniter =
        igniter
        |> Igniter.assign(:prompt_on_git_changes?, false)
        |> Igniter.assign(:quiet_on_no_changes?, true)

      result_igniter = Mix.Tasks.Drops.Relation.GenSchemas.igniter(igniter)
      Igniter.do_or_dry_run(result_igniter, yes: true)

      # Run again with sync mode (default)
      igniter2 = Igniter.new()

      args2 = %Igniter.Mix.Task.Args{
        argv: ["--app", "SampleApp", "--namespace", "SampleApp.Generated", "--sync", "true"],
        argv_flags: ["--app", "SampleApp", "--namespace", "SampleApp.Generated", "--sync", "true"]
      }

      igniter2 = %{igniter2 | args: args2}

      igniter2 =
        igniter2
        |> Igniter.assign(:prompt_on_git_changes?, false)
        |> Igniter.assign(:quiet_on_no_changes?, true)

      result_igniter2 = Mix.Tasks.Drops.Relation.GenSchemas.igniter(igniter2)
      Igniter.do_or_dry_run(result_igniter2, yes: true)

      # Compile and load the generated modules
      compile_generated_modules()

      # Verify the modules are still valid
      assert_valid_schema_module(SampleApp.Generated.Users, "users")
      assert_valid_schema_module(SampleApp.Generated.Posts, "posts")
    end
  end

  # Helper function to verify a generated schema module is valid
  defp assert_valid_schema_module(module, expected_table_name) do
    # Ensure the module is loaded
    assert {:module, ^module} = Code.ensure_loaded(module)

    # Verify it's an Ecto schema
    assert function_exported?(module, :__schema__, 1)

    # Verify the table name
    assert module.__schema__(:source) == expected_table_name

    # Verify it has fields (should have at least id and timestamps)
    fields = module.__schema__(:fields)
    assert :id in fields
    assert :inserted_at in fields
    assert :updated_at in fields

    # Verify specific fields based on the table
    case expected_table_name do
      "users" ->
        assert :email in fields
        assert :first_name in fields
        assert :last_name in fields
        assert :age in fields
        assert :is_active in fields

      "posts" ->
        assert :title in fields
        assert :body in fields
        assert :published in fields
        assert :user_id in fields
    end

    # Verify field types are correct
    assert module.__schema__(:type, :id) == :id
    assert module.__schema__(:type, :inserted_at) == :naive_datetime
    assert module.__schema__(:type, :updated_at) == :naive_datetime
  end

  # Compile and load generated modules
  defp compile_generated_modules do
    generated_files = Path.wildcard("lib/sample_app/generated/*.ex")

    Enum.each(generated_files, fn file ->
      case File.read(file) do
        {:ok, content} ->
          case Code.eval_string(content) do
            {_, _} -> :ok
            _ -> :error
          end

        {:error, _} ->
          :error
      end
    end)
  end

  # Clean up generated files
  defp cleanup_generated_files do
    generated_files = Path.wildcard("lib/sample_app/generated/*.ex")
    Enum.each(generated_files, &File.rm/1)

    # Also remove the directory if it's empty
    case File.ls("lib/sample_app/generated") do
      {:ok, []} -> File.rmdir("lib/sample_app/generated")
      _ -> :ok
    end
  end

  # Clean up any generated modules to avoid conflicts between tests
  defp cleanup_generated_modules do
    modules_to_cleanup = [
      SampleApp.Generated.Users,
      SampleApp.Generated.Posts,
      SampleApp.Generated.Comments
    ]

    Enum.each(modules_to_cleanup, fn module ->
      if Code.ensure_loaded?(module) do
        :code.purge(module)
        :code.delete(module)
      end
    end)
  end
end
