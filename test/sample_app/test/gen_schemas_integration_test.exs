defmodule GenSchemasIntegrationTest do
  use ExUnit.Case, async: false

  @moduletag :integration

  # Clean up generated modules after each test
  setup do
    on_exit(fn ->
      cleanup_generated_modules()
    end)
  end

  describe "mix drops.relation.gen_schemas" do
    test "generates valid Ecto schema modules for all tables" do
      # Run the mix task to generate schemas
      {output, exit_code} = System.cmd("mix", [
        "drops.relation.gen_schemas",
        "--app", "SampleApp",
        "--namespace", "SampleApp.Generated"
      ], cd: ".", stderr_to_stdout: true)

      # Verify the task completed successfully
      assert exit_code == 0, "Task failed with output: #{output}"
      assert output =~ "Creating new schema: SampleApp.Generated.Users"
      assert output =~ "Creating new schema: SampleApp.Generated.Posts"

      # Verify the generated modules can be loaded and are valid Ecto schemas
      assert_valid_schema_module(SampleApp.Generated.Users, "users")
      assert_valid_schema_module(SampleApp.Generated.Posts, "posts")
    end

    test "generates schemas for specific tables only" do
      # Run the mix task for only the users table
      {output, exit_code} = System.cmd("mix", [
        "drops.relation.gen_schemas",
        "--app", "SampleApp",
        "--namespace", "SampleApp.Generated",
        "--tables", "users"
      ], cd: ".", stderr_to_stdout: true)

      # Verify the task completed successfully
      assert exit_code == 0, "Task failed with output: #{output}"
      assert output =~ "Creating new schema: SampleApp.Generated.Users"
      refute output =~ "Creating new schema: SampleApp.Generated.Posts"

      # Verify only the users module was generated
      assert_valid_schema_module(SampleApp.Generated.Users, "users")
      refute Code.ensure_loaded?(SampleApp.Generated.Posts)
    end

    test "sync mode updates existing schemas" do
      # First, generate the schemas
      {_output, exit_code} = System.cmd("mix", [
        "drops.relation.gen_schemas",
        "--app", "SampleApp",
        "--namespace", "SampleApp.Generated"
      ], cd: ".", stderr_to_stdout: true)

      assert exit_code == 0

      # Run again with sync mode (default)
      {output, exit_code} = System.cmd("mix", [
        "drops.relation.gen_schemas",
        "--app", "SampleApp",
        "--namespace", "SampleApp.Generated",
        "--sync", "true"
      ], cd: ".", stderr_to_stdout: true)

      # Verify the task completed successfully
      assert exit_code == 0, "Sync task failed with output: #{output}"
      assert output =~ "Creating or updating schema: SampleApp.Generated.Users"
      assert output =~ "Creating or updating schema: SampleApp.Generated.Posts"

      # Verify the modules are still valid
      assert_valid_schema_module(SampleApp.Generated.Users, "users")
      assert_valid_schema_module(SampleApp.Generated.Posts, "posts")
    end
  end

  # Helper function to verify a generated schema module is valid
  defp assert_valid_schema_module(module, expected_table_name) do
    # Ensure the module is loaded
    assert {:module, ^module} = Code.ensure_loaded(module)

    # Verify it's an Ecto schema
    assert function_exported?(module, :__schema__, 1)

    # Verify the table name
    assert module.__schema__(:source) == expected_table_name

    # Verify it has fields (should have at least id and timestamps)
    fields = module.__schema__(:fields)
    assert :id in fields
    assert :inserted_at in fields
    assert :updated_at in fields

    # Verify specific fields based on the table
    case expected_table_name do
      "users" ->
        assert :email in fields
        assert :first_name in fields
        assert :last_name in fields
        assert :age in fields
        assert :is_active in fields

      "posts" ->
        assert :title in fields
        assert :body in fields
        assert :published in fields
        assert :user_id in fields
    end

    # Verify field types are correct
    assert module.__schema__(:type, :id) == :id
    assert module.__schema__(:type, :inserted_at) == :naive_datetime
    assert module.__schema__(:type, :updated_at) == :naive_datetime
  end

  # Clean up any generated modules to avoid conflicts between tests
  defp cleanup_generated_modules do
    modules_to_cleanup = [
      SampleApp.Generated.Users,
      SampleApp.Generated.Posts,
      SampleApp.Generated.Comments
    ]

    Enum.each(modules_to_cleanup, fn module ->
      if Code.ensure_loaded?(module) do
        :code.purge(module)
        :code.delete(module)
      end
    end)
  end
end
